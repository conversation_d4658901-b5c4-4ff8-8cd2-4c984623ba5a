#!/bin/bash

echo "🔄 恢复代理设置..."

# 检查备份文件是否存在
if [ -f "/Users/<USER>/Library/Application Support/Code/User/settings.json.proxy_backup" ]; then
    # 恢复原始设置
    cp "/Users/<USER>/Library/Application Support/Code/User/settings.json.proxy_backup" "/Users/<USER>/Library/Application Support/Code/User/settings.json"
    
    # 添加Gemini相关设置到恢复的配置中
    python3 -c "
import json

settings_path = '/Users/<USER>/Library/Application Support/Code/User/settings.json'

with open(settings_path, 'r') as f:
    settings = json.load(f)

# 添加Gemini设置但保留代理
settings.update({
    'cloudcode.beta.forceOobLogin': True,
    'geminicodeassist.verboseLogging': True,
    'http.proxyStrictSSL': False
})

with open(settings_path, 'w') as f:
    json.dump(settings, f, indent=4)

print('代理设置已恢复，Gemini设置已保留')
"
    
    echo "✅ 代理设置已恢复！"
    echo "重启VSCode以应用更改..."
    
    # 重启VSCode
    killall "Visual Studio Code" 2>/dev/null || true
    sleep 2
    code . &
    
else
    echo "❌ 未找到代理设置备份文件"
fi
