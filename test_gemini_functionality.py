# Gemini Code Assist 功能测试文件
# 请在VSCode中打开此文件来测试各项功能

print("🧪 Gemini Code Assist 功能测试")

# 测试1: 代码补全
# 在下面这行输入 "def " 然后等待代码补全建议
# def 

# 测试2: 注释生成代码
# 下面的注释应该触发代码生成建议
# 创建一个计算斐波那契数列的函数

# 测试3: 代码解释和优化
def inefficient_function():
    """这是一个效率不高的函数，可以请求Gemini优化"""
    result = []
    for i in range(100):
        for j in range(100):
            if i == j:
                result.append(i)
    return result

# 测试4: 错误修复
# 下面的代码有语法错误，可以测试Gemini的修复建议
# def broken_function(
#     print("This has a syntax error"

# 测试5: 单元测试生成
def add_numbers(a, b):
    """简单的加法函数，可以请求Gemini生成单元测试"""
    return a + b

# 测试6: 文档生成
def complex_function(data, options=None):
    if options is None:
        options = {}
    
    processed_data = []
    for item in data:
        if isinstance(item, dict):
            processed_data.append(item.get('value', 0))
        else:
            processed_data.append(item)
    
    return sum(processed_data) / len(processed_data) if processed_data else 0

if __name__ == "__main__":
    print("请在VSCode中测试以下功能：")
    print("1. 代码补全 - 输入代码时查看自动建议")
    print("2. 聊天功能 - 打开Gemini聊天面板")
    print("3. 代码生成 - 使用注释触发代码生成")
    print("4. 代码解释 - 选中代码并请求解释")
    print("5. 错误修复 - 对有错误的代码使用快速修复")
