#!/bin/bash

echo "🔄 修复Gemini Code Assist登录转圈问题..."

# 1. 杀死所有相关进程
echo "1. 停止所有相关进程..."
killall "Visual Studio Code" 2>/dev/null || true
pkill -f "cloudcode_cli" 2>/dev/null || true
sleep 3

# 2. 清理认证缓存
echo "2. 清理认证缓存..."
rm -rf "/Users/<USER>/Library/Application Support/Code/User/globalStorage/google.*" 2>/dev/null || true
rm -rf "/Users/<USER>/Library/Application Support/Code/User/globalStorage/googlecloudtools.*" 2>/dev/null || true
rm -rf "/Users/<USER>/Library/Application Support/Code/logs" 2>/dev/null || true

# 3. 清理cloudcode缓存
echo "3. 清理cloudcode缓存..."
rm -rf "/Users/<USER>/Library/Application Support/cloud-code" 2>/dev/null || true

# 4. 临时禁用代理设置
echo "4. 创建临时无代理配置..."
python3 -c "
import json
import os

settings_path = '/Users/<USER>/Library/Application Support/Code/User/settings.json'

# 读取现有设置
try:
    with open(settings_path, 'r') as f:
        settings = json.load(f)
except:
    settings = {}

# 备份原始设置
with open(settings_path + '.proxy_backup', 'w') as f:
    json.dump(settings, f, indent=4)

# 临时禁用代理
settings_no_proxy = settings.copy()
if 'http.proxy' in settings_no_proxy:
    del settings_no_proxy['http.proxy']
if 'https.proxy' in settings_no_proxy:
    del settings_no_proxy['https.proxy']

# 添加强制登录设置
settings_no_proxy.update({
    'cloudcode.beta.forceOobLogin': True,
    'geminicodeassist.verboseLogging': True,
    'http.proxyStrictSSL': False
})

# 写入临时设置
with open(settings_path, 'w') as f:
    json.dump(settings_no_proxy, f, indent=4)

print('已创建无代理临时配置')
"

# 5. 重新启动VSCode
echo "5. 重新启动VSCode..."
sleep 2
code . &

echo "✅ 修复完成！"
echo ""
echo "📋 现在请尝试："
echo "1. 等待VSCode完全启动（约10-15秒）"
echo "2. 点击状态栏的Gemini图标"
echo "3. 选择'Login to Google'"
echo "4. 如果还是转圈，请关闭登录窗口并重试"
echo ""
echo "如果成功登录后，运行以下命令恢复代理设置："
echo "cp '/Users/<USER>/Library/Application Support/Code/User/settings.json.proxy_backup' '/Users/<USER>/Library/Application Support/Code/User/settings.json'"
