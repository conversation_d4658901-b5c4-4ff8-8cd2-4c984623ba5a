#!/bin/bash

echo "🔧 修复Gemini Code Assist扩展..."

# 1. 停止VSCode进程
echo "1. 停止VSCode进程..."
killall "Visual Studio Code" 2>/dev/null || true
sleep 2

# 2. 备份当前设置
echo "2. 备份当前设置..."
cp "/Users/<USER>/Library/Application Support/Code/User/settings.json" "/Users/<USER>/Library/Application Support/Code/User/settings.json.backup.$(date +%Y%m%d_%H%M%S)"

# 3. 合并Gemini修复设置
echo "3. 应用Gemini修复设置..."
python3 -c "
import json
import os

settings_path = '/Users/<USER>/Library/Application Support/Code/User/settings.json'
fix_settings_path = './gemini_fix_settings.json'

# 读取现有设置
try:
    with open(settings_path, 'r') as f:
        settings = json.load(f)
except:
    settings = {}

# 读取修复设置
with open(fix_settings_path, 'r') as f:
    fix_settings = json.load(f)

# 合并设置
settings.update(fix_settings)

# 写回设置文件
with open(settings_path, 'w') as f:
    json.dump(settings, f, indent=4)

print('设置已更新')
"

# 4. 重新启动VSCode
echo "4. 重新启动VSCode..."
code . &

echo "✅ 修复完成！"
echo ""
echo "📋 接下来的步骤："
echo "1. 等待VSCode完全启动"
echo "2. 点击状态栏的Gemini图标"
echo "3. 选择'Login to Google'重新登录"
echo "4. 测试代码补全功能"
echo ""
echo "如果问题仍然存在，请尝试："
echo "- 禁用代理设置测试"
echo "- 重新安装Gemini Code Assist扩展"
