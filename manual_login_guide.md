# Gemini Code Assist 手动登录指南

## 🔄 如果登录页面仍然转圈，请按以下步骤操作：

### 方法1: 浏览器手动登录
1. 打开浏览器，访问：https://accounts.google.com
2. 登录您的Google账户
3. 确保账户有Gemini Code Assist权限
4. 返回VSCode，重新尝试登录

### 方法2: 命令行登录
```bash
# 使用gcloud CLI登录（如果已安装）
gcloud auth login
gcloud auth application-default login
```

### 方法3: 重置扩展
```bash
# 完全重新安装扩展
code --uninstall-extension google.geminicodeassist
code --install-extension google.geminicodeassist
```

### 方法4: 检查网络连接
```bash
# 测试Google服务连接
curl -I https://accounts.google.com
curl -I https://oauth2.googleapis.com
```

## 🔧 故障排除步骤

### 如果登录成功但功能不工作：
1. 检查状态栏Gemini图标状态
2. 尝试在Python文件中输入代码测试补全
3. 打开Gemini聊天面板测试

### 如果需要恢复代理设置：
```bash
./restore_proxy.sh
```

### 如果问题持续存在：
1. 检查Google账户是否有Gemini Code Assist权限
2. 确认是否在企业网络环境中（可能需要管理员权限）
3. 尝试使用个人网络环境测试

## 📞 获取更多帮助
- 查看VSCode输出面板中的"Gemini Code Assist"日志
- 检查开发者工具控制台错误信息
- 联系Google Cloud支持
